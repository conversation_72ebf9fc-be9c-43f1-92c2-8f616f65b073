import { useSignal } from "@preact/signals";
import { IS_BROWSER } from "$fresh/runtime.ts";

interface LoginFormProps {
  redirectTo?: string | null;
}

export default function LoginForm({ redirectTo }: LoginFormProps) {
  const email = useSignal("");
  const password = useSignal("");
  const rememberMe = useSignal(false);
  const isLoading = useSignal(false);
  const error = useSignal<string | null>(null);

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!IS_BROWSER) return;

    if (!email.value || !password.value) {
      error.value = "Please fill in all fields";
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email.value,
          password: password.value,
          rememberMe: rememberMe.value,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to dashboard or specified redirect URL
        const redirectUrl = redirectTo || "/";
        window.location.href = redirectUrl;
      } else {
        error.value = result.error || "Login failed";
      }
    } catch (err) {
      console.error("Login error:", err);
      error.value = "Network error. Please try again.";
    } finally {
      isLoading.value = false;
    }
  };

  const fillDemoCredentials = () => {
    email.value = "<EMAIL>";
    password.value = "demo123";
  };

  return (
    <form onSubmit={handleSubmit} class="space-y-6">
      {/* Error Message */}
      {error.value && (
        <div class="p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error.value}
        </div>
      )}

      {/* Email Field */}
      <div>
        <label htmlFor="email" class="block text-sm font-medium text-gray-700">
          Email address
        </label>
        <div class="mt-1">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email.value}
            onInput={(e) => email.value = (e.target as HTMLInputElement).value}
            class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Enter your email"
          />
        </div>
      </div>

      {/* Password Field */}
      <div>
        <label htmlFor="password" class="block text-sm font-medium text-gray-700">
          Password
        </label>
        <div class="mt-1">
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={password.value}
            onInput={(e) => password.value = (e.target as HTMLInputElement).value}
            class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Enter your password"
          />
        </div>
      </div>

      {/* Remember Me */}
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            checked={rememberMe.value}
            onChange={(e) => rememberMe.value = (e.target as HTMLInputElement).checked}
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label htmlFor="remember-me" class="ml-2 block text-sm text-gray-900">
            Remember me
          </label>
        </div>

        <div class="text-sm">
          <button
            type="button"
            onClick={fillDemoCredentials}
            class="font-medium text-primary-600 hover:text-primary-500"
          >
            Use demo credentials
          </button>
        </div>
      </div>

      {/* Submit Button */}
      <div>
        <button
          type="submit"
          disabled={isLoading.value}
          class={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
            isLoading.value
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-primary-600 hover:bg-primary-700"
          }`}
        >
          {isLoading.value && (
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
          {isLoading.value ? "Signing in..." : "Sign in"}
        </button>
      </div>
    </form>
  );
}
