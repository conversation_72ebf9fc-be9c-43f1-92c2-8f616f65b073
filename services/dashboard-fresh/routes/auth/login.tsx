import { defineRoute } from "$fresh/server.ts";
import LoginForm from "../../islands/auth/LoginForm.tsx";

export default defineRoute(async (req, ctx) => {
  // Redirect if already authenticated
  if (ctx.state.isAuthenticated) {
    const url = new URL(req.url);
    const redirect = url.searchParams.get("redirect") || "/";
    return new Response("", {
      status: 302,
      headers: { Location: redirect },
    });
  }

  const url = new URL(req.url);
  const redirect = url.searchParams.get("redirect");
  const error = url.searchParams.get("error");

  return (
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Logo */}
        <div class="flex justify-center">
          <svg class="h-12 w-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        
        <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">
          Sign in to your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Access your e-commerce analytics dashboard
        </p>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow-soft sm:rounded-lg sm:px-10">
          {/* Error Message */}
          {error && (
            <div class="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
              {error === "invalid_credentials" && "Invalid email or password"}
              {error === "session_expired" && "Your session has expired. Please sign in again."}
              {error === "unauthorized" && "You need to sign in to access this page"}
              {!["invalid_credentials", "session_expired", "unauthorized"].includes(error) && "An error occurred. Please try again."}
            </div>
          )}

          {/* Login Form */}
          <LoginForm redirectTo={redirect} />

          {/* Footer Links */}
          <div class="mt-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">Need help?</span>
              </div>
            </div>

            <div class="mt-6 grid grid-cols-2 gap-3">
              <a
                href="/auth/forgot-password"
                class="text-center text-sm text-primary-600 hover:text-primary-500"
              >
                Forgot password?
              </a>
              <a
                href="/auth/register"
                class="text-center text-sm text-primary-600 hover:text-primary-500"
              >
                Create account
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Credentials */}
      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="text-sm font-medium text-blue-800 mb-2">Demo Credentials</h3>
          <div class="text-xs text-blue-700 space-y-1">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> demo123</p>
          </div>
        </div>
      </div>
    </div>
  );
});
