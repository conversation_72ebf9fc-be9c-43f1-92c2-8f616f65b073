import { MiddlewareHandlerContext } from "$fresh/server.ts";
import { verifyJWT } from "../utils/auth.ts";
import { getCookies } from "$std/http/cookie.ts";

export async function handler(
  req: Request,
  ctx: MiddlewareHandlerContext,
) {
  // Skip auth for public routes
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/api/health'];
  const isPublicRoute = publicRoutes.some(route => ctx.url.pathname.startsWith(route));
  
  if (isPublicRoute) {
    return ctx.next();
  }

  // Try to get token from Authorization header or cookie
  let token: string | null = null;
  
  const authHeader = req.headers.get("Authorization");
  if (authHeader?.startsWith("Bearer ")) {
    token = authHeader.substring(7);
  } else {
    // Fallback to cookie for browser requests
    const cookies = getCookies(req.headers);
    token = cookies.auth_token || null;
  }

  if (token) {
    try {
      const user = await verifyJWT(token);
      ctx.state.user = user;
      ctx.state.isAuthenticated = true;
    } catch (error) {
      console.error("Auth verification failed:", error);
      ctx.state.user = null;
      ctx.state.isAuthenticated = false;
    }
  } else {
    ctx.state.user = null;
    ctx.state.isAuthenticated = false;
  }

  // Redirect unauthenticated users to login for protected routes
  if (!ctx.state.isAuthenticated && !isPublicRoute) {
    // For API routes, return 401
    if (ctx.url.pathname.startsWith('/api/')) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
    
    // For page routes, redirect to login
    const loginUrl = `/auth/login?redirect=${encodeURIComponent(ctx.url.pathname)}`;
    return new Response("", {
      status: 302,
      headers: { Location: loginUrl },
    });
  }

  return ctx.next();
}
